[2025-07-01 10:20:34] local.INFO: getInvoiceData called for penawaran ID: 11  
[2025-07-01 10:20:34] local.INFO: Penawaran has invoice:  {"invoice_id":117} 
[2025-07-01 10:20:34] local.INFO: getInvoiceData called for penawaran ID: 11  
[2025-07-01 10:20:34] local.INFO: Penawaran has invoice:  {"invoice_id":117} 
[2025-07-01 10:20:46] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-07-01 10:20:46] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
[2025-07-01 10:20:46] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-07-01 10:20:46] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
[2025-07-01 10:20:52] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-05-18  
[2025-07-01 10:21:06] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":2360,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"INSTALL NEW AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-07-01 10:21:06] local.INFO: Part 0 is custom, validating part_code, part_name, and additional fields  
[2025-07-01 10:21:06] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":1182,"quantity":1,"price":"0.00","status":"Not Ready","is_custom":false,"part_code":"210223-PWB","part_name":"REG CS250 ACET 150KPA 2GA"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-01 10:21:06] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-07-01 10:21:06] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":2360,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"INSTALL NEW AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-07-01 10:21:06] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-07-01 10:21:06] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250519-2928"} 
[2025-07-01 10:21:06] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250519-2928","part_inventory_id":2356} 
[2025-07-01 10:35:39] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-01 10:48:42] local.WARNING: Part not found for inventory ID: 2025 with part_code: PCH-R134a-PWB  
[2025-07-01 10:48:42] local.WARNING: Part not found for inventory ID: 2026 with part_code: PCL-R134a-PWB  
[2025-07-01 10:48:43] local.WARNING: Part not found for inventory ID: 1819 with part_code: PCH-R134a-PWB  
[2025-07-01 10:48:43] local.WARNING: Part not found for inventory ID: 1820 with part_code: PCL-R134a-PWB  
[2025-07-01 10:52:24] local.ERROR: Undefined variable $collection (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $collection (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $collection at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\xampp\\\\htdocs...', 127)
#1 C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php(127): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\xampp\\\\htdocs...', 127)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}
"} 
[2025-07-01 10:52:31] local.ERROR: syntax error, unexpected token ";", expecting variable or "{" or "$" (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected token \";\", expecting variable or \"{\" or \"$\" (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \";\", expecting variable or \"{\" or \"$\" at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-01 10:52:35] local.ERROR: Undefined variable $siteid (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $siteid (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ErrorException), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $siteid at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:127)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\xampp\\\\htdocs...', 127)
#1 C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php(127): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\xampp\\\\htdocs...', 127)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#59 {main}
"} 
[2025-07-01 10:55:32] local.ERROR: Unclosed '(' does not match '}' (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unclosed '(' does not match '}' (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ParseError(code: 0): Unclosed '(' does not match '}' at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-01 10:55:33] local.ERROR: Unclosed '(' does not match '}' (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unclosed '(' does not match '}' (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ParseError(code: 0): Unclosed '(' does not match '}' at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-01 10:55:54] local.ERROR: syntax error, unexpected token ")" (View: C:\xampp\htdocs\portalpwb\resources\views\warehouse\confirmation.blade.php) {"userId":"00002","exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected token \")\" (View: C:\\xampp\\htdocs\\portalpwb\\resources\\views\\warehouse\\confirmation.blade.php) at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#55 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\portalpwb\\storage\\framework\\views\\e26d0972b43b02cfb9b3763b12c0417c.php:128)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminRole.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminRole->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-01 11:02:01] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-01 11:02:02] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-01 11:02:02] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:02:02] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:02:02] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:02:02] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:02:02] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:02:02] local.INFO: Sites Data Response {"count":5} 
[2025-07-01 11:02:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:02:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:02:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:03:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:03:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:03:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:03:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:03:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:04:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:04:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:04:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:04:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:04:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:05:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:05:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:05:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:05:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:05:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:06:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:06:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:06:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:07:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:07:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:07:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:24] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:25] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:31] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:33] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:35] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:37] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:37] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":"AC","site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:42] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:42] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":"AC","site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:47] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:49] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:53] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:55] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:56] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:07:59] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:00] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:10] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:11] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:13] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:13] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:16] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":"AC","site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:16] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":"TYRE","site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:17] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":"AC","site":"PPA","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:20] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:21] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-06-30","month":null,"division":"AC","site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-07-01 11:08:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:08:44] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:50] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:51] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:51] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:52] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:53] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:53] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:55] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:56] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:08:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:09:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:09:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:09:52] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:09:54] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:09:56] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:10:01] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"IMK","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:10:03] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"IMK","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:10:04] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"IMK","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:10:07] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"IMK","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:11:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:11:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:12:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:12:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:14:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:14:20] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-01 11:14:20] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:14:20] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:14:20] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:14:20] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:14:20] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:14:20] local.INFO: Sites Data Response {"count":5} 
[2025-07-01 11:14:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:14:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:14:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"PPA","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:16:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:16:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:16:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:19:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:19:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:20:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:22:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:23:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:24:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:24:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:25:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:25:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:25:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:26:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:27:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:29:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"PPA","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:29:15] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:16] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:19] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:29:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:29:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"FABRIKASI","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:29:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:29:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:30:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:31:40] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:41] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:43] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:43] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:43] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:44] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:45] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:45] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:45] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:49] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:31:57] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:31:57] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:32:02] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:32:04] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:32:05] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:32:07] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:34:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:34:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:34:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:34:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:34:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:34:56] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:35:01] local.INFO: Best Parts Data Request {"start_date":"2025-05-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:02] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:03] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:04] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:04] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:26] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:26] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:27] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:28] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:28] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:35:47] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:48] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:50] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:50] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:35:51] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:36:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:36:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:36:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:36:10] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:36:11] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:37:58] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:37:58] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:00] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:00] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:01] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:01] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:01] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:02] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:02] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:38:02] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:39:48] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:48] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:50] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:51] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:51] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:51] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:51] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:52] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:52] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:52] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:52] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:39:53] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:09] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:09] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:10] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:10] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:13] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:13] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:13] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:14] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:41] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:42] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:42] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:40:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:40:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:40:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:41:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:41:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:43:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:43:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:43:20] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:43:21] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:43:24] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:43:25] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:43:26] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:44:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:44:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:44:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:45:11] local.INFO: Best Parts Data Request {"start_date":"2025-05-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:45:12] local.INFO: Best Parts Data Request {"start_date":"2025-05-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:45:33] local.INFO: Best Parts Data Request {"start_date":"2025-05-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:45:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:45:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:46:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:46:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:46:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:46:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:46:28] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:46:29] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:46:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:47:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:24] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:25] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:28] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:28] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:31] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:32] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:48:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:48:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:14] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:49:15] local.INFO: Best Parts Data Request {"start_date":"2025-05-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-05-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:49:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:29] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:49:30] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:49:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:49:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:38] local.INFO: Best Parts Data Request {"start_date":"2025-06-06","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-06 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:50:38] local.INFO: Best Parts Data Request {"start_date":"2025-06-06","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-06 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:50:50] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-01 11:50:50] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:50:50] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:50:50] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:50:50] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:50:50] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-01 11:50:50] local.INFO: Sites Data Response {"count":5} 
[2025-07-01 11:50:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:50:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:51:00] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:51:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:31] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:32] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:36] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:36] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:36] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:52:51] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:52] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:53] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:54] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:52:54] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:53:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:53:10] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:55:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:55:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:55:14] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:57:40] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:41] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:45] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:45] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:46] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:46] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:46] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:49] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:50] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:51] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:52] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:53] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:54] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:55] local.INFO: Best Parts Data Request {"start_date":"2025-06-02","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-02 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:57] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:57:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:58:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:27] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 11:59:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 11:59:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:00:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:55] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:55] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:57] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:57] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:57] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:57] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:58] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:59] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:59] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:00:59] local.INFO: Best Parts Data Request {"start_date":"2025-06-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:01:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-01 12:03:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"FABRIKASI","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"TYRE","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:33] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":"AC","site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:36] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
[2025-07-01 12:03:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-01","month":null,"division":null,"site":"DH","used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-01 23:59:59"} 
